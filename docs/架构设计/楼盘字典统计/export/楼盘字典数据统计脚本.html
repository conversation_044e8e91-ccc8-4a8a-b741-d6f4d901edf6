<!DOCTYPE html>
<html>

<head>

<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
<title>楼盘字典数据统计脚本</title>


<style type="text/css">
body {
  font-family: Helvetica, arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: white;
  padding: 30px; }

body > *:first-child {
  margin-top: 0 !important; }
body > *:last-child {
  margin-bottom: 0 !important; }

a {
  color: #4183C4; }
a.absent {
  color: #cc0000; }
a.anchor {
  display: block;
  padding-left: 30px;
  margin-left: -30px;
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0; }

h1, h2, h3, h4, h5, h6 {
  margin: 20px 0 10px;
  padding: 0;
  font-weight: bold;
  -webkit-font-smoothing: antialiased;
  cursor: text;
  position: relative; }

h1:hover a.anchor, h2:hover a.anchor, h3:hover a.anchor, h4:hover a.anchor, h5:hover a.anchor, h6:hover a.anchor {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA09pVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoMTMuMCAyMDEyMDMwNS5tLjQxNSAyMDEyLzAzLzA1OjIxOjAwOjAwKSAgKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6OUM2NjlDQjI4ODBGMTFFMTg1ODlEODNERDJBRjUwQTQiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6OUM2NjlDQjM4ODBGMTFFMTg1ODlEODNERDJBRjUwQTQiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo5QzY2OUNCMDg4MEYxMUUxODU4OUQ4M0REMkFGNTBBNCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo5QzY2OUNCMTg4MEYxMUUxODU4OUQ4M0REMkFGNTBBNCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PsQhXeAAAABfSURBVHjaYvz//z8DJYCRUgMYQAbAMBQIAvEqkBQWXI6sHqwHiwG70TTBxGaiWwjCTGgOUgJiF1J8wMRAIUA34B4Q76HUBelAfJYSA0CuMIEaRP8wGIkGMA54bgQIMACAmkXJi0hKJQAAAABJRU5ErkJggg==) no-repeat 10px center;
  text-decoration: none; }

h1 tt, h1 code {
  font-size: inherit; }

h2 tt, h2 code {
  font-size: inherit; }

h3 tt, h3 code {
  font-size: inherit; }

h4 tt, h4 code {
  font-size: inherit; }

h5 tt, h5 code {
  font-size: inherit; }

h6 tt, h6 code {
  font-size: inherit; }

h1 {
  font-size: 28px;
  color: black; }

h2 {
  font-size: 24px;
  border-bottom: 1px solid #cccccc;
  color: black; }

h3 {
  font-size: 18px; }

h4 {
  font-size: 16px; }

h5 {
  font-size: 14px; }

h6 {
  color: #777777;
  font-size: 14px; }

p, blockquote, ul, ol, dl, li, table, pre {
  margin: 15px 0; }

hr {
  background: transparent url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAECAYAAACtBE5DAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6OENDRjNBN0E2NTZBMTFFMEI3QjRBODM4NzJDMjlGNDgiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6OENDRjNBN0I2NTZBMTFFMEI3QjRBODM4NzJDMjlGNDgiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo4Q0NGM0E3ODY1NkExMUUwQjdCNEE4Mzg3MkMyOUY0OCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo4Q0NGM0E3OTY1NkExMUUwQjdCNEE4Mzg3MkMyOUY0OCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PqqezsUAAAAfSURBVHjaYmRABcYwBiM2QSA4y4hNEKYDQxAEAAIMAHNGAzhkPOlYAAAAAElFTkSuQmCC) repeat-x 0 0;
  border: 0 none;
  color: #cccccc;
  height: 4px;
  padding: 0;
}

body > h2:first-child {
  margin-top: 0;
  padding-top: 0; }
body > h1:first-child {
  margin-top: 0;
  padding-top: 0; }
  body > h1:first-child + h2 {
    margin-top: 0;
    padding-top: 0; }
body > h3:first-child, body > h4:first-child, body > h5:first-child, body > h6:first-child {
  margin-top: 0;
  padding-top: 0; }

a:first-child h1, a:first-child h2, a:first-child h3, a:first-child h4, a:first-child h5, a:first-child h6 {
  margin-top: 0;
  padding-top: 0; }

h1 p, h2 p, h3 p, h4 p, h5 p, h6 p {
  margin-top: 0; }

li p.first {
  display: inline-block; }
li {
  margin: 0; }
ul, ol {
  padding-left: 30px; }

ul :first-child, ol :first-child {
  margin-top: 0; }

dl {
  padding: 0; }
  dl dt {
    font-size: 14px;
    font-weight: bold;
    font-style: italic;
    padding: 0;
    margin: 15px 0 5px; }
    dl dt:first-child {
      padding: 0; }
    dl dt > :first-child {
      margin-top: 0; }
    dl dt > :last-child {
      margin-bottom: 0; }
  dl dd {
    margin: 0 0 15px;
    padding: 0 15px; }
    dl dd > :first-child {
      margin-top: 0; }
    dl dd > :last-child {
      margin-bottom: 0; }

blockquote {
  border-left: 4px solid #dddddd;
  padding: 0 15px;
  color: #777777; }
  blockquote > :first-child {
    margin-top: 0; }
  blockquote > :last-child {
    margin-bottom: 0; }

table {
  padding: 0;border-collapse: collapse; }
  table tr {
    border-top: 1px solid #cccccc;
    background-color: white;
    margin: 0;
    padding: 0; }
    table tr:nth-child(2n) {
      background-color: #f8f8f8; }
    table tr th {
      font-weight: bold;
      border: 1px solid #cccccc;
      margin: 0;
      padding: 6px 13px; }
    table tr td {
      border: 1px solid #cccccc;
      margin: 0;
      padding: 6px 13px; }
    table tr th :first-child, table tr td :first-child {
      margin-top: 0; }
    table tr th :last-child, table tr td :last-child {
      margin-bottom: 0; }

img {
  max-width: 100%; }

span.frame {
  display: block;
  overflow: hidden; }
  span.frame > span {
    border: 1px solid #dddddd;
    display: block;
    float: left;
    overflow: hidden;
    margin: 13px 0 0;
    padding: 7px;
    width: auto; }
  span.frame span img {
    display: block;
    float: left; }
  span.frame span span {
    clear: both;
    color: #333333;
    display: block;
    padding: 5px 0 0; }
span.align-center {
  display: block;
  overflow: hidden;
  clear: both; }
  span.align-center > span {
    display: block;
    overflow: hidden;
    margin: 13px auto 0;
    text-align: center; }
  span.align-center span img {
    margin: 0 auto;
    text-align: center; }
span.align-right {
  display: block;
  overflow: hidden;
  clear: both; }
  span.align-right > span {
    display: block;
    overflow: hidden;
    margin: 13px 0 0;
    text-align: right; }
  span.align-right span img {
    margin: 0;
    text-align: right; }
span.float-left {
  display: block;
  margin-right: 13px;
  overflow: hidden;
  float: left; }
  span.float-left span {
    margin: 13px 0 0; }
span.float-right {
  display: block;
  margin-left: 13px;
  overflow: hidden;
  float: right; }
  span.float-right > span {
    display: block;
    overflow: hidden;
    margin: 13px auto 0;
    text-align: right; }

code, tt {
  margin: 0 2px;
  padding: 0 5px;
  white-space: nowrap;
  border: 1px solid #eaeaea;
  background-color: #f8f8f8;
  border-radius: 3px; }

pre code {
  margin: 0;
  padding: 0;
  white-space: pre;
  border: none;
  background: transparent; }

.highlight pre {
  background-color: #f8f8f8;
  border: 1px solid #cccccc;
  font-size: 13px;
  line-height: 19px;
  overflow: auto;
  padding: 6px 10px;
  border-radius: 3px; }

pre {
  background-color: #f8f8f8;
  border: 1px solid #cccccc;
  font-size: 13px;
  line-height: 19px;
  overflow: auto;
  padding: 6px 10px;
  border-radius: 3px; }
  pre code, pre tt {
    background-color: transparent;
    border: none; }

sup {
    font-size: 0.83em;
    vertical-align: super;
    line-height: 0;
}

kbd {
  display: inline-block;
  padding: 3px 5px;
  font-size: 11px;
  line-height: 10px;
  color: #555;
  vertical-align: middle;
  background-color: #fcfcfc;
  border: solid 1px #ccc;
  border-bottom-color: #bbb;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #bbb
}

* {
	-webkit-print-color-adjust: exact;
}
@media screen and (min-width: 914px) {
    body {
        width: 854px;
        margin:0 auto;
    }
}
@media print {
	table, pre {
		page-break-inside: avoid;
	}
	pre {
		word-wrap: break-word;
	}
  body {
    padding: 2cm; 
  }
}
</style>


</head>

<body>

<ul class="toc">
<li>
<a href="#toc_0">1.小区表</a>
</li>
<li>
<a href="#toc_1">2.小区详情:小区各项指标信息</a>
</li>
<li>
<a href="#toc_2">3.小区户型</a>
</li>
<li>
<a href="#toc_3">4.小区图片表</a>
</li>
<li>
<a href="#toc_4">5.楼栋信息</a>
</li>
<li>
<a href="#toc_5">6.单元信息</a>
</li>
<li>
<a href="#toc_6">7.房屋信息</a>
</li>
<li>
<a href="#toc_7">8.楼盘信息</a>
</li>
<li>
<a href="#toc_8">9. 新房楼盘预售证</a>
</li>
<li>
<a href="#toc_9">10. 新房楼盘图片</a>
</li>
<li>
<a href="#toc_10">11. 挂牌房源</a>
</li>
</ul>


<h3 id="toc_0">1.小区表</h3>

<div><pre><code class="language-sql">select *
from (select c.city_name                                    as 城市
           , count(1)                                       as 小区数
           , sum(if(c.`address` is not null, 1, 0))         as 有地址
           , sum(if(c.`location` is not null, 1, 0))        as 有坐标
           , sum(if(c.`district_name` is not null, 1, 0))   as 有行政区
           , sum(if(c.`town_name` is not null, 1, 0))       as 有街道
           , sum(if(c.`busi_name` is not null, 1, 0))       as 有商圈
           , sum(if(c.`around_response` is not null, 1, 0)) as 有周边
      from tb_community c
      GROUP BY c.city_name) t1
where 小区数 &gt; 500
order by 小区数 desc;</code></pre></div>

<h3 id="toc_1">2.小区详情:小区各项指标信息</h3>

<div><pre><code class="language-sql">select t1.*
from (select c.city_name                                                                    as 城市
           , count(1)                                                                       as 总数
           , sum(if(c.`green_rate` is not null and c.green_rate &lt;&gt; 0, 1, 0))                as 绿化率
           , sum(if(c.`house_num` is not null and c.house_num &lt;&gt; 0, 1, 0))                  as 房屋总数
           , sum(if(c.`parking_num` is not null and c.parking_num &lt;&gt; 0, 1, 0))              as 车位总数
           , sum(if(c.`volume_rate` is not null and c.volume_rate &lt;&gt; 0, 1, 0))              as 容积率
           , sum(if(c.`build_max_year` is not null and c.build_max_year &lt;&gt; &#39;&#39;, 1, 0))       as 建筑年代最大
           , sum(if(c.`build_min_year` is not null and c.build_min_year &lt;&gt; &#39;&#39;, 1, 0))       as 建成年代最小
           , sum(if(c.`building_type` is not null and c.building_type &lt;&gt; &#39;&#39;, 1, 0))         as 建筑类型
           , sum(if(c.`build_num` is not null and c.build_num &lt;&gt; 0, 1, 0))                  as 楼栋总数
           , sum(if(c.`developer_corp` is not null and c.developer_corp &lt;&gt; &#39;&#39;, 1, 0))       as 交易权属
           , sum(if(c.`gas_desc` is not null and c.gas_desc &lt;&gt; &#39;&#39;, 1, 0))                   as 供气描述
           , sum(if(c.`heating_desc` is not null and c.heating_desc &lt;&gt; &#39;&#39;, 1, 0))           as 供暖情况
           , sum(if(c.`parking_rate` is not null and c.parking_rate &lt;&gt; &#39;&#39;, 1, 0))           as 车位配比率
           , sum(if(c.`pic_url` is not null and c.pic_url &lt;&gt; &#39;&#39;, 1, 0))                     as 小区缩略图地址
           , sum(if(c.`powerd_desc` is not null and c.powerd_desc &lt;&gt; &#39;&#39;, 1, 0))             as 供电描述
           , sum(if(c.`property_fee` is not null and c.property_fee &lt;&gt; &#39;&#39;, 1, 0))           as 物业费
           , sum(if(c.`property_name` is not null and c.property_name &lt;&gt; &#39;&#39;, 1, 0))         as 物业公司
           , sum(if(c.`property_phone` is not null and c.property_phone &lt;&gt; &#39;&#39;, 1, 0))       as 物业电话
           , sum(if(c.`property_type` is not null and c.property_type &lt;&gt; &#39;&#39;, 1, 0))         as 物业类型
           , sum(if(c.`property_years` is not null and c.property_years &lt;&gt; &#39;&#39;, 1, 0))       as 产权年限
           , sum(if(c.`set_parking_fee` is not null and c.set_parking_fee &lt;&gt; &#39;&#39;, 1, 0))     as 固定停车费标准
           , sum(if(c.`water_desc` is not null and c.water_desc &lt;&gt; &#39;&#39;, 1, 0))               as 供水描述
           , sum(if(c.`down_parking_num` is not null and c.down_parking_num &lt;&gt; 0, 1, 0))    as 地下车位数
           , sum(if(c.`parking_sale_flag` is not null, 1, 0))                               as 是否出售产权车位
           , sum(if(c.`person_div_car_flag` is not null, 1, 0))                             as 是否人车分流
           , sum(if(c.`building_category` is not null and c.building_category &lt;&gt; &#39;&#39;, 1, 0)) as 建筑类别
           , sum(if(c.`house_type` is not null and c.house_type &lt;&gt; &#39;&#39;, 1, 0))               as 房屋类型
           , sum(if(c.`act_area` is not null and c.act_area &lt;&gt; 0, 1, 0))                    as 占地面积
           , sum(if(c.`build_area` is not null and c.build_area &lt;&gt; 0, 1, 0))                as 建筑面积
      from tb_community_detail c
      GROUP BY c.city_name) t1
where 总数 &gt; 500
order by 总数 desc</code></pre></div>

<h3 id="toc_2">3.小区户型</h3>

<div><pre><code class="language-sql">select t1.*
from (select cd.city_name                                                          as 城市
           , count(1)                                                              as 总数
           , sum(if(c.`area` is not null and c.area &lt;&gt; 0, 1, 0))                   as 面积
           , sum(if(c.`pic_url2` is not null and c.pic_url2 &lt;&gt; &#39;&#39;, 1, 0))          as 户型图
           , sum(if(c.`balcony_count` is not null and c.balcony_count &lt;&gt; 0, 1, 0)) as 阳台数
           , sum(if(c.`hall_count` is not null and c.hall_count &lt;&gt; 0, 1, 0))       as 客厅数
           , sum(if(c.`kitchen_count` is not null and c.kitchen_count &lt;&gt; 0, 1, 0)) as 厨房数
           , sum(if(c.`room_count` is not null and c.room_count &lt;&gt; 0, 1, 0))       as 房间数
           , sum(if(c.`toilet_count` is not null and c.toilet_count &lt;&gt; 0, 1, 0))   as 卫生间数
           , sum(if(c.`set_num` is not null and c.set_num &lt;&gt; 0, 1, 0))             as 总套数
           , sum(if(c.`name` is not null and c.name &lt;&gt; &#39;&#39;, 1, 0))                  as 户型名
      from tb_community_layout c
               join tb_community_detail cd on cd.id = c.community_detail_id
      GROUP BY cd.city_name) t1
where 总数 &gt; 500
order by 总数 desc</code></pre></div>

<h3 id="toc_3">4.小区图片表</h3>

<div><pre><code class="language-sql">select t1.*
from (select cd.city_name                          as 城市
           , count(distinct c.community_detail_id) as 小区数
           , count(1)                              as 图片数
           , sum(if(c.type = &#39;PASSAGEWAY&#39;, 1, 0))  as 出入口
           , sum(if(c.type = &#39;DISTANT&#39;, 1, 0))     as 远景
           , sum(if(c.type = &#39;ROAD&#39;, 1, 0))        as 道路
           , sum(if(c.type = &#39;PARK&#39;, 1, 0))        as 远景图
           , sum(if(c.type = &#39;BUILDING&#39;, 1, 0))    as 楼栋
           , sum(if(c.type = &#39;ENTRY_DOOR&#39;, 1, 0))  as 入户门
           , sum(if(c.type = &#39;SCENERY&#39;, 1, 0))     as 景观带
           , sum(if(c.type = &#39;FACILITY&#39;, 1, 0))    as 配套
           , sum(if(c.type = &#39;DISTRIBUTE&#39;, 1, 0))  as 分布图
           , sum(if(c.type = &#39;OTHER&#39;, 1, 0))       as 其他
      from tb_community_picture c
               join tb_community_detail cd on cd.id = c.community_detail_id
      GROUP BY cd.city_name) t1
where 图片数 &gt; 500
order by 图片数 desc</code></pre></div>

<h3 id="toc_4">5.楼栋信息</h3>

<div><pre><code class="language-sql">SELECT *
from (SELECT c.city_name                                                                        as 城市,
             count(DISTINCT c.id)                                                               as 小区数,
             count(1)                                                                           as 楼栋数,
             sum(if(b.source_building_addr is not null and b.source_building_addr &lt;&gt; &#39;&#39;, 1, 0)) as 单元地址,
             sum(if(b.baidu_lng is not null and b.baidu_lng &lt;&gt; &#39;&#39;, 1, 0))                       as 百度经度,
             sum(if(b.baidu_lat is not null and b.baidu_lat &lt;&gt; &#39;&#39;, 1, 0))                       as 百度纬度,
             sum(if(b.gaode_lng is not null and b.gaode_lng &lt;&gt; &#39;&#39;, 1, 0))                       as 高德经度,
             sum(if(b.gaode_lat is not null and b.gaode_lat &lt;&gt; &#39;&#39;, 1, 0))                       as 高德纬度,
             sum(if(b.set_num is not null and b.set_num &lt;&gt; &#39;&#39;, 1, 0))                           as 总套数,
             sum(if(b.floor_num is not null and b.floor_num &lt;&gt; 0, 1, 0))                        as 实际层高,
             sum(if(b.start_floor is not null and b.start_floor &lt;&gt; 0, 1, 0))                    as 起始楼层,
             sum(if(b.highest_floor is not null and b.highest_floor &lt;&gt; 0, 1, 0))                as 最高楼层,
             sum(if(b.elevator_num is not null and b.elevator_num &lt;&gt; &#39;&#39;, 1, 0))                 as 电梯数量,
             sum(if(b.house_elevator_rate is not null and b.house_elevator_rate &lt;&gt; &#39;&#39;, 1, 0))   as 梯户比,
             sum(if(b.distance is not null and b.distance &lt;&gt; 0, 1, 0))                          as 距离小区中心
      from building b
               left JOIN community c on c.community_id = b.community_id
      group by c.city_name) t1
order by 楼栋数 desc</code></pre></div>

<h3 id="toc_5">6.单元信息</h3>

<div><pre><code class="language-sql">SELECT *
from (SELECT c.city_name                                                         as 城市,
             count(DISTINCT c.id)                                                as 小区数,
             count(1)                                                            as 单元数,
             sum(if(b.unit_name is not null and b.unit_name &lt;&gt; &#39;&#39;, 1, 0))        as 单元地址,
             sum(if(b.set_num is not null and b.set_num &lt;&gt; &#39;&#39;, 1, 0))            as 总套数,
             sum(if(b.floor_num is not null and b.floor_num &lt;&gt; 0, 1, 0))         as 实际层高,
             sum(if(b.start_floor is not null and b.start_floor &lt;&gt; 0, 1, 0))     as 起始楼层,
             sum(if(b.highest_floor is not null and b.highest_floor &lt;&gt; 0, 1, 0)) as 最高楼层
      from unit b
               left JOIN community c on c.community_id = b.community_id
      group by c.city_name) t1
order by 单元数 desc</code></pre></div>

<h3 id="toc_6">7.房屋信息</h3>

<div><pre><code class="language-sql">SELECT *
from (SELECT c.city_name                                                 as 城市,
             count(DISTINCT c.id)                                        as 小区数,
             count(1)                                                    as 房屋数,
             sum(if(b.room_no is not null and b.room_no &lt;&gt; &#39;&#39;, 1, 0))    as 房号,
             sum(if(b.nom_floor is not null and b.nom_floor &lt;&gt; 0, 1, 0)) as 名义楼层,
             sum(if(b.act_floor is not null and b.act_floor &lt;&gt; 0, 1, 0)) as 实际楼层
      from room b
               left JOIN community c on c.community_id = b.community_id
      group by c.city_name) t1
order by 房屋数 desc</code></pre></div>

<h3 id="toc_7">8.楼盘信息</h3>

<div><pre><code class="language-sql">select *
from (select xbi.city                                                                                           as 城市
           , count(1)                                                                                              楼盘数
           , sum(if(highlights is not null and highlights &lt;&gt; &#39;&#39;, 1, 0))                                         as 楼盘亮点
           , sum(if(tag_codes is not null and tag_codes &lt;&gt; &#39;&#39;, 1, 0))                                           as 楼盘标签
           , sum(if(avg_price is not null and avg_price &lt;&gt; &#39;&#39;, 1, 0))                                           as 楼盘均价
           , sum(if(show_price is not null and show_price &lt;&gt; &#39;&#39;, 1, 0))                                         as 楼盘展示价
           , sum(if(estate_types is not null and estate_types &lt;&gt; &#39;&#39;, 1, 0))                                     as 物业类型
           , sum(if(building_area is not null and building_area &lt;&gt; &#39;&#39;, 1, 0))                                   as 建筑面积
           , sum(if(project_detail_address is not null and project_detail_address &lt;&gt; &#39;&#39;, 1, 0))                 as 楼盘详细地址
           , sum(if(map_location_longitude is not null and map_location_longitude &lt;&gt; &#39;&#39;, 1, 0))                 as 地图定位
           , sum(if(developer_full_name is not null and developer_full_name &lt;&gt; &#39;&#39;, 1, 0))                       as 开发商全称
           , sum(if(developer_sales_office_address is not null and developer_sales_office_address &lt;&gt; &#39;&#39;, 1, 0)) as 售楼处地址
           , sum(if(developer_opening_time is not null and developer_opening_time &lt;&gt; &#39;&#39;, 1, 0))                 as 开盘时间
           , sum(if(developer_due_time is not null and developer_due_time &lt;&gt; &#39;&#39;, 1, 0))                         as 交付时间
           , sum(if(community_area is not null and community_area &lt;&gt; &#39;&#39;, 1, 0))                                 as 占地面积
           , sum(if(community_volume_rate is not null and community_volume_rate &lt;&gt; &#39;&#39;, 1, 0))                   as 容积率
           , sum(if(community_greening_rate is not null and community_greening_rate &lt;&gt; &#39;&#39;, 1, 0))               as 绿化率
           , sum(if(community_planning_building is not null and community_planning_building &lt;&gt; &#39;&#39;, 1, 0))       as 规划楼栋
           , sum(if(community_planning_households is not null and community_planning_households &lt;&gt; &#39;&#39;, 1, 0))   as 规划户数
           , sum(if(community_estate_company is not null and community_estate_company &lt;&gt; &#39;&#39;, 1, 0))             as 物业公司
           , sum(if(community_estate_expenses is not null and community_estate_expenses &lt;&gt; &#39;&#39;, 1, 0))           as 物业费用
           , sum(if(community_heating_type_desc is not null and community_heating_type_desc &lt;&gt; &#39;&#39;, 1, 0))       as 供暖方式
           , sum(if(community_water_type_desc is not null and community_water_type_desc &lt;&gt; &#39;&#39;, 1, 0))           as 供水方式
           , sum(if(community_power_type_desc is not null and community_power_type_desc &lt;&gt; &#39;&#39;, 1, 0))           as 供电方式
           , sum(if(overall_introduction is not null and overall_introduction &lt;&gt; &#39;&#39;, 1, 0))                     as 楼盘介绍
           , sum(if(phone_no is not null and phone_no &lt;&gt; &#39;&#39;, 1, 0))                                             as 售楼处座机
           , sum(if(parking_radio is not null and parking_radio &lt;&gt; &#39;&#39;, 1, 0))                                   as 车位比例
           , sum(if(alias_names is not null and alias_names &lt;&gt; &#39;&#39;, 1, 0))                                       as 楼盘别名
           , sum(if(building_types is not null and building_types &lt;&gt; &#39;&#39;, 1, 0))                                 as 建筑类型
           , sum(if(house_types is not null and house_types &lt;&gt; &#39;&#39;, 1, 0))                                       as 房屋类型
           , sum(if(traffic_description is not null and traffic_description &lt;&gt; &#39;&#39;, 1, 0))                       as 交通状况
           , sum(if(peripheral_business is not null and peripheral_business &lt;&gt; &#39;&#39;, 1, 0))                       as 周边商业
           , sum(if(surrounding_landscape is not null and surrounding_landscape &lt;&gt; &#39;&#39;, 1, 0))                   as 周边景观
           , sum(if(surrounding_parks is not null and surrounding_parks &lt;&gt; &#39;&#39;, 1, 0))                           as 周边公园
           , sum(if(surrounding_hospitals is not null and surrounding_hospitals &lt;&gt; &#39;&#39;, 1, 0))                   as 周边医院
           , sum(if(surrounding_schools is not null and surrounding_schools &lt;&gt; &#39;&#39;, 1, 0))                       as 周边学校
           , sum(if(surrounding_traffic is not null and surrounding_traffic &lt;&gt; &#39;&#39;, 1, 0))                       as 周边交通
           , sum(if(start_time is not null and start_time &lt;&gt; &#39;&#39;, 1, 0))                                         as 开工时间
           , sum(if(completion_time is not null and completion_time &lt;&gt; &#39;&#39;, 1, 0))                               as 竣工时间
           , sum(if(house_acquisition_rate is not null and house_acquisition_rate &lt;&gt; &#39;&#39;, 1, 0))                 as 得房率
           , sum(if(parking_number is not null and parking_number &lt;&gt; 0, 1, 0))                                  as 车位数
           , sum(if(parking_fee is not null and parking_fee &lt;&gt; &#39;待定&#39;, 1, 0))                                   as 车位费
           , sum(if(main_house_type_description is not null and main_house_type_description &lt;&gt; &#39;&#39;, 1, 0))       as 主力户型
           , sum(if(diversion_people_vehicles is not null, 1, 0))                                               as 人车分流
      from xf_base_info xbi
      where city is not null
        and city &lt;&gt; &#39;&#39;
      GROUP BY xbi.city) t1
order by 楼盘数 desc</code></pre></div>

<h3 id="toc_8">9. 新房楼盘预售证</h3>

<div><pre><code class="language-sql">select *
from (select bi.city                                                                              as 城市
           , count(1)                                                                                预售证数
           , sum(if(pre_sale_license_number is not null and pre_sale_license_number &lt;&gt; &#39;&#39;, 1, 0)) as 许可证号
           , sum(if(certification_date is not null and certification_date &lt;&gt; &#39;&#39;, 1, 0))           as 发证日期
           , sum(if(permitted_sales_area is not null and permitted_sales_area &lt;&gt; &#39;&#39;, 1, 0))       as 准许销售面积
           , sum(if(pre_sale_position is not null and pre_sale_position &lt;&gt; &#39;&#39;, 1, 0))             as 预售部位
      from xf_pre_sale_info si
               join xf_base_info bi on bi.outer_id = si.outer_id
      where bi.city is not null
        and bi.city &lt;&gt; &#39;&#39;
      GROUP BY bi.city) t1
order by 预售证数 desc
;</code></pre></div>

<h3 id="toc_9">10. 新房楼盘图片</h3>

<div><pre><code class="language-sql">select *
from (select bi.city as                  城市
           , count(DISTINCT si.outer_id) 楼盘数
           , count(1)                    图片数
      from xf_picture_info si
               join xf_base_info bi on bi.outer_id = si.outer_id
      where bi.city is not null
        and bi.city &lt;&gt; &#39;&#39;
      GROUP BY bi.city) t1
order by 楼盘数 desc
;</code></pre></div>

<h3 id="toc_10">11. 挂牌房源</h3>

<div><pre><code class="language-sql">select *
from (select d.city_name                             as 城市
           , count(DISTINCT d.broker_id)             as 经纪人数
           , sum(if(channel_code = &#39;XIANYU&#39;, 1, 0))  as 闲鱼
           , sum(if(channel_code = &#39;ALIPAY&#39;, 1, 0))  as 支付宝
           , sum(if(channel_code = &#39;PRIVATE&#39;, 1, 0)) as 微信个人店
           , count(1)                                as 合计
      from tb_delegation d
      where d.channel_code is not null
        and d.`status` = &#39;UP_SUCC&#39;
        and d.logic_delete = 0
      GROUP BY d.city_name) t1
order by 闲鱼 desc;</code></pre></div>




</body>

</html>
