package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.community.*;
import com.ejuetc.consumer.web.vo.CommunityLayoutVO;
import com.ejuetc.consumer.web.vo.CommunityTipsVO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;
import static java.lang.Thread.sleep;


//@RunWith(SpringRunner.class)
//@SpringBootTest
public class CommunityTest {


    //    @Autowired
    private CommunityWebImpl communityWeb;
    SaaSApiSDK sdk = new SaaSApiSDK(
            "cd0badf8c664484a9500e3f1a5351623",
            "RDX6D3bPTwIReEmi06D0PJz9VJ2WthhTmj2WYs7fXdI=",
            "http://localhost:8095"
//                "http://saasapi-test.ebaas.com/gateway/invoke"
    );

//    @Autowired
//    private CommunityApiImpl communityAPI;
//    private CommunityAPI communityAPI = sdk.feignClient(CommunityAPI.class, "http://ejuetc-consumer.tst.ejucloud.cn/consumer");
    private CommunityAPI communityAPI = getAPI(CommunityAPI.class, "http://ejuetc-consumer.tst.ejucloud.cn/consumer");
//    private CommunityAPI communityAPI = getAPI(CommunityAPI.class, "http://localhost:8097/consumer");
    //    private CommunityAPI communityAPI = getAPI(CommunityAPI.class, "http://ejuetc-consumer.uat.ejucloud.cn");
//    private CommunityAPI communityAPI = getAPI(CommunityAPI.class, "http://ejuetc-consumer.ejucloud.cn");

    @Test
    public void queryTips() {
        ApiResponse<List<CommunityTipsVO>> response = communityAPI.queryCommunityNames(new QueryTipsPO("上海", "慧芝湖"));
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void queryHasDetailByName() {
        ApiResponse<List<CommunityVO>> response = communityAPI.queryHasDetailByName(new QueryCommunityPO(310100L, "慧芝湖", 5, 1));
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void queryLayoutNames() {
        ApiResponse<Map<Integer, Set<String>>> response = communityAPI.queryLayoutGroup(new BindCommunityPO("上海", "慧芝湖"));
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void queryDetail() {
        ApiResponse<CommunityVO> response = communityAPI.queryDetail(new BindCommunityPO("上海", "慧芝湖"));
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void testBindCommunity() {
        List.of("上海金山@旭辉光耀城").forEach(community -> {
            String[] split = community.split("@");
            ApiResponse<BindCommunityRO> response = communityAPI.bind(split[0], split[1]);
            System.out.println(JSON.toJSONString(response, true));
        });
    }

//    @Test
//    public void testList() {
//        ApiResponse<List<CommunityVO>> communities =
//                communityImpl.list(new LoginToken(), 310000L, "慧", 5, 1);
//        System.out.println(JSON.toJSONString(communities));
//    }

    @Test
    public void testBindCommunityDetail() throws InterruptedException {
        List<String> cityCodes = List.of(
                "3709",
                "5101",
                "3205",
                "4502",
                "3412",
                "1201",
                "3301",
                "6101",
                "4401",
                "3713",
                "3502",
                "5301",
                "3201",
                "4101",
                "3202",
                "4601",
                "4403",
                "3702",
                "5001",
                "3601",
                "4503",
                "3505"
        );
        ApiResponse<?> response = communityAPI.bindCommunityDetail(null, 1, 1);
        System.out.println(response);
        sleep(3 * 60 * 1000);
    }

    @SneakyThrows
    @Test
    public void testRebind() {
        ApiResponse<?> response = communityAPI.rebind(1000, 100);
        System.out.println(response);
//        sleep(3 * 60 * 1000);
    }

    @SneakyThrows
    @Test
    public void testUpdateOverlapRate() {
        ApiResponse<?> response = communityAPI.updateOverlapRate(2000);
        System.out.println(response);
//        sleep(3 * 60 * 1000);
    }

    @Test
    public void testSearch() {
        ApiResponse<List<CommunityTipsVO>> response = communityWeb.queryByTips(new SaasLoginToken(), "海口", "金色假日", 1, 10);
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void testSearchPictures() {
        ApiResponse<Map<String, List<String>>> response = communityWeb.queryCommunityPictures(new SaasLoginToken(), "上海静安", "慧芝湖");
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void testSearchLayout() {
        ApiResponse<List<CommunityLayoutVO>> response = communityWeb.queryCommunityLayouts(new SaasLoginToken(), "上海静安", "慧芝湖");
        System.out.println(JSON.toJSONString(response, true));
    }
}