package com.ejuetc.consumer.api.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.ejuetc.consumer.domain.region.Region")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "地区")
public class RegionDTO extends BaseDTO<RegionDTO> {

    public static final Long CITY_ID_CHENGDU = 510100L;
    public static final Long CITY_ID_XUZHOU = 320300L;
    public static final List<Long> GOV_VERIFY_CITY_IDS = List.of(CITY_ID_CHENGDU, CITY_ID_XUZHOU);

    @Getter
    public enum Type implements TitleEnum {
        PROVINCE("省级", null),
        CITY("市级", PROVINCE),
        DISTRICT("区级", CITY),
        TOWN("镇级", DISTRICT),
        BUSI("商圈", CITY),
        ;

        private final String title;
        private final Type parent;

        Type(String title, Type parent) {
            this.title = title;
            this.parent = parent;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum XyBusiType implements TitleEnum {
        FREE_EXCLUSIVE("免费独家"),
        FREE_NONEXCLUSIVE("免费非独家"),
        PAID_NONEXCLUSIVE("付费非独家"),
        ;

        private final String title;

        XyBusiType(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum FeatureCode implements TitleEnum {
        HOUSE_VALUE_ASSESSMENT("二手房价值评估"),
        ;

        private final String title;

        FeatureCode(String title) {
            this.title = title;
        }

        @Override
        public String getTitle() {
            return this.title;
        }

         @Override
         public String toString() {
             return name() + "-" + title;
         }
    }

    public RegionDTO(Long userId) {
        super(userId);
    }

    @QueryField
    @Schema(description = "地区类型")
    private RegionDTO.Type type;

    @QueryField
    @Schema(description = "闲鱼业务类型")
    private RegionDTO.XyBusiType xyBusiType;

    @QueryField
    @Schema(description = "国标编码")
    private String code;

    @QueryField
    @Schema(description = "高德AD编码")
    private String amapAdcode;

    @QueryField
    @Schema(description = "高德城市编码")
    private String amapCitycode;

    @QueryField
    @Schema(description = "全称")
    private String name;

    @QueryField
    @Schema(description = "全拼")
    private String pinyinFull;

    @QueryField
    @Schema(description = "拼音首字母")
    private String pinyinInitials;

    @QueryField
    @Schema(description = "拼音第一个字母")
    private String pinyinFirst;

    @QueryField
    @Schema(description = "简称")
    private String shortName;

    @QueryField
    @Schema(description = "别名")
    protected String alias;

    @QueryField
    @Schema(description = "所属上级地区")
    private RegionDTO parent;

    @QueryField
    @Schema(description = "上级地区名称")
    private String parentName;

    @QueryField
    @Schema(description = "子地区列表")
    private List<RegionDTO> children;

    @QueryField
    @Schema(description = "所属城市ID")
    private RegionDTO city;

    @QueryField
    @Schema(description = "所属城市名称")
    private String cityName;

    @QueryField
    @Schema(description = "支持的功能编码")
    private List<FeatureCode> featureCodes;


}
