package com.ejuetc.consumer.web.vo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.querydomain.api.LoadPolicy;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import com.ejuetc.commons.base.usertype.ListUT;
import com.ejuetc.consumer.api.dto.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ejuetc.commons.base.querydomain.api.LoadPolicy.ALWAYS_LOAD;
import static com.ejuetc.commons.base.querydomain.api.LoadPolicy.SPECIFY_LOAD;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "小区")
@QueryDomain("com.ejuetc.consumer.domain.community.Community")
public class CommunityVO {

    @QueryField
    @Schema(description = "主键")
    private Long id;

    @QueryField
    @Schema(description = "(高德)经度")
    protected String location;

    @QueryField
    @Schema(description = "小区名称")
    protected String name;

    @QueryField
    @Schema(description = "小区地址")
    protected String address;

    @QueryField("city.id")
    @Schema(description = "城市主键")
    protected Long cityId;

    @QueryField
    @Schema(description = "城市名")
    protected String cityName;

    @QueryField("city.code")
    @Schema(description = "城市编码")
    protected String cityCode;

    @QueryField
    @Schema(description = "省份名")
    protected String provinceName;

    @QueryField("district.id")
    @Schema(description = "行政区主键")
    protected Long districtId;

    @QueryField
    @Schema(description = "行政区名称")
    protected String districtName;

    @QueryField
    @Schema(description = "商圈名称")
    private String busiName;

    @Schema(description = "周边")
    @QueryField(value = "around", loadPolicy = SPECIFY_LOAD)
    protected AroundWrapper around;

    @Accessors(chain = true)
    @Data
    public static class Around {
        private String id;
        private String name;
        private Float distance;
        private String address;
        private String location;
        private String type;
        private String typecode;
    }

    @Accessors(chain = true)
    @Data
    public static class AroundWrapper {
        @Schema(description = "公交站")
        private List<Around> bus = new ArrayList<>();

        @Schema(description = "地铁站")
        private List<Around> subway = new ArrayList<>();

        @Schema(description = "幼儿园")
        private List<Around> kindergarten = new ArrayList<>();

        @Schema(description = "小学")
        private List<Around> primarySchool = new ArrayList<>();

        @Schema(description = "初中")
        private List<Around> middleSchool = new ArrayList<>();

        @Schema(description = "医院")
        private List<Around> hospital = new ArrayList<>();

        @Schema(description = "药店")
        private List<Around> pharmacy = new ArrayList<>();

        @Schema(description = "购物")
        private List<Around> shopping = new ArrayList<>();

        @Schema(description = "餐饮")
        private List<Around> restaurant = new ArrayList<>();

        public List<String> getTypes() {
            List<String> types = new ArrayList<>();
            if (!bus.isEmpty()) {
                types.add("公交");
            }
            if (!subway.isEmpty()) {
                types.add("地铁房");
            }
            if (!kindergarten.isEmpty() || !primarySchool.isEmpty() || !middleSchool.isEmpty()) {
                types.add("学校");
            }
            if (!hospital.isEmpty()) {
                types.add("医院");
            }
            if (!shopping.isEmpty() || !pharmacy.isEmpty()) {
                types.add("购物");
            }
            if (!restaurant.isEmpty()) {
                types.add("餐饮");
            }
            return types;
        }
    }

    @QueryField
    @Schema(description = "POI类型")
    private String typeName;

    @QueryField
    @Schema(description = "(高德)经度")
    private BigDecimal longitude;

    @QueryField
    @Schema(description = "纬度")
    private BigDecimal latitude;

    @QueryField
    @Schema(description = "格式化小区地址")
    protected String formattedAddress;

    @QueryField
    @Schema(description = "城镇/街道名称")
    private String townName;

    @QueryField
    @Schema(description = "小区详情")
    private CommunityDetailVO detail;


    @QueryField(loadPolicy = LoadPolicy.SPECIFY_LOAD)
    @Schema(description = "小区图片映射")
    private Map<String, List<String>> picturesMap;

    @QueryField(loadPolicy = LoadPolicy.SPECIFY_LOAD)
    @Schema(description = "户型汇总映射")
    private Map<String, CommunityLayoutSummaryVO> layoutsSummaryMap;

    @QueryField(loadPolicy = LoadPolicy.SPECIFY_LOAD)
    @Schema(description = "户型名称列表")
    private List<Integer> layoutNames;

    @QueryField(loadPolicy = LoadPolicy.SPECIFY_LOAD)
    @Schema(description = "户型房间数列表")
    private List<Integer> layoutRoomCounts;

    @QueryField
    @Schema(description = "小区户型")
    private List<CommunityLayoutVO> layouts;

    @QueryField(loadPolicy = ALWAYS_LOAD)
    @Schema(description = "贝壳小区ID列表")
    private List<Long> beikeIds;

}
