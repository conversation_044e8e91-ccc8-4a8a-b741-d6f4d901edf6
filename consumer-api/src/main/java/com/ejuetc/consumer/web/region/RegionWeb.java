package com.ejuetc.consumer.web.region;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.RegionDTO;
import com.ejuetc.consumer.web.vo.RegionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "WEB_地区")
@FeignClient(value = "ejuetc.consumer", path = "/consumer", contextId = "RegionWeb")
public interface RegionWeb {
    enum GroupType {
        LETTER,
        TYPE
    }

    @Operation(summary = "业务开通城市列表")
    @GetMapping("/region/businessOpenCities")
    ApiResponse<List<RegionVO>> businessOpenCities();

    @Operation(summary = "地区列表")
    @GetMapping("/region/list")
    ApiResponse<Object> list(
            @Parameter(description = "父级地区Id") @RequestParam(name = "parentCode", required = false) Long parentRegionId,
            @Parameter(description = "所属城市Id") @RequestParam(required = false) Long parentCityId,
            @Parameter(description = "地区类型") @RequestParam(required = false) RegionDTO.Type type,
            @Parameter(description = "功能编码") @RequestParam(required = false) RegionDTO.FeatureCode featureCode,
            @Parameter(description = "查询关键字") @RequestParam(required = false) String keyword,
            @Parameter(description = "分组类型") @RequestParam(required = false) GroupType groupType);


}
